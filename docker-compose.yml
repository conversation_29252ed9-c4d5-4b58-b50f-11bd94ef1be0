version: '3.8'

services:
  web:
    image: brettt89/silverstripe-web:latest
    container_name: silverstripe-web
    working_dir: /var/www
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - .:/var/www
      - ./docker/logs/apache:/var/log/apache2
    environment:
      - DOCUMENT_ROOT=/var/www/public
      - APACHE_RUN_USER=www-data
      - APACHE_RUN_GROUP=www-data
      - SS_DATABASE_SERVER=database
      - SS_DATABASE_NAME=silverstripe
      - SS_DATABASE_USERNAME=silverstripe
      - SS_DATABASE_PASSWORD=silverstripe
      - SS_DEFAULT_ADMIN_USERNAME=admin
      - SS_DEFAULT_ADMIN_PASSWORD=admin
      - SS_ENVIRONMENT_TYPE=dev
      - SS_DATABASE_CHOOSE_NAME=false
    depends_on:
      - database
    networks:
      - silverstripe-network

  database:
    image: mysql:8.0
    container_name: silverstripe-database
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/logs/mysql:/var/log/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=silverstripe
      - MYSQL_USER=silverstripe
      - MYSQL_PASSWORD=silverstripe
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - silverstripe-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: silverstripe-phpmyadmin
    ports:
      - "8080:80"
    environment:
      - PMA_HOST=database
      - PMA_USER=silverstripe
      - PMA_PASSWORD=silverstripe
    depends_on:
      - database
    networks:
      - silverstripe-network

volumes:
  mysql_data:

networks:
  silverstripe-network:
    driver: bridge
